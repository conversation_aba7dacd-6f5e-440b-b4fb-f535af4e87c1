<div class="flex min-h-screen items-center justify-center">
  <div class="w-full max-w-md p-8 space-y-8 bg-base-200 rounded-lg shadow-lg">
    <div class="text-center">
      <h1 class="text-2xl font-bold">Reset your password</h1>
      <p class="mt-2 text-sm">Enter your email address and we'll send you a link to reset your password</p>
    </div>

    <div *ngIf="success" class="alert alert-success">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <div>
        <h3 class="font-bold">Email sent!</h3>
        <div class="text-xs">Check your email for password reset instructions.</div>
      </div>
    </div>

    <form *ngIf="!success" [formGroup]="requestForm" (ngSubmit)="onSubmit()" class="mt-8 space-y-6">
      <div class="space-y-4">
        <div>
          <label for="email" class="block text-sm font-medium">Email address</label>
          <input
            id="email"
            type="email"
            formControlName="email"
            class="w-full input input-bordered mt-1"
            placeholder="<EMAIL>"
            [ngClass]="{'input-error': requestForm.get('email')?.invalid && requestForm.get('email')?.touched}"
          />
          <div *ngIf="requestForm.get('email')?.invalid && requestForm.get('email')?.touched" class="text-error text-sm mt-1">
            <span *ngIf="requestForm.get('email')?.errors?.['required']">Email is required</span>
            <span *ngIf="requestForm.get('email')?.errors?.['email']">Please enter a valid email</span>
          </div>
        </div>
      </div>

      <div *ngIf="error" class="alert alert-error">
        {{ error }}
      </div>

      <div>
        <button
          type="submit"
          class="w-full btn btn-primary"
          [disabled]="requestForm.invalid || loading"
        >
          <span *ngIf="loading" class="loading loading-spinner loading-xs"></span>
          Send reset link
        </button>
      </div>
    </form>

    <div class="text-center text-sm space-y-2">
      <p>Remember your password? <a [routerLink]="CoreUrls.login" class="text-primary">Sign in</a></p>
      <p>Don't have an account? <a [routerLink]="CoreUrls.register" class="text-primary">Sign up</a></p>
    </div>
  </div>
</div>
