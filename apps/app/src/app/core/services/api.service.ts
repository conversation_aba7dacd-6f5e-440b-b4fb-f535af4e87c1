import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { AuthLoginRequestDto } from '../dto/auth-login-request.dto';
import { AuthResponseDto } from '../dto/auth-response.dto';
import { UserMeDto } from '../dto/user-me.dto';
import { CreateUserRequestDto } from '../dto/create-user-request.dto';
import { UserRegistrationResponseDto } from '../dto/user-registration-response.dto';
import { ResendEmailVerificationResponseDto } from '../dto/resend-email-verification-response.dto';
import { CreateOrganizationDto } from '../dto/create-organization.dto';
import { OrganizationDto } from '../dto/organization.dto';
import { SwitchOrganizationRequestDto } from '../dto/switch-organization-request.dto';
import { PasswordResetRequestDto, PasswordResetChangeDto, PasswordResetResponseDto } from '../dto/password-reset.dto';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  // Auth endpoints
  login(loginData: AuthLoginRequestDto): Observable<AuthResponseDto> {
    return this.http.post<AuthResponseDto>(`${this.apiUrl}/auth/login`, loginData);
  }

  getCurrentUser(): Observable<UserMeDto> {
    return this.http.get<UserMeDto>(`${this.apiUrl}/auth/me`);
  }

  // User endpoints
  register(userData: CreateUserRequestDto): Observable<UserRegistrationResponseDto> {
    return this.http.post<UserRegistrationResponseDto>(`${this.apiUrl}/users`, userData);
  }

  verifyEmail(userId: string, code: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/users/confirm-email`, { userId, code });
  }

  resendEmailVerification(email: string): Observable<ResendEmailVerificationResponseDto> {
    return this.http.post<ResendEmailVerificationResponseDto>(`${this.apiUrl}/users/resend-email-verification`, { email });
  }

  // Organization endpoints
  getOrganizations(): Observable<OrganizationDto[]> {
    return this.http.get<OrganizationDto[]>(`${this.apiUrl}/organizations`);
  }

  createOrganization(data: CreateOrganizationDto): Observable<OrganizationDto> {
    return this.http.post<OrganizationDto>(`${this.apiUrl}/organizations`, data);
  }

  switchOrganization(organizationId: string): Observable<AuthResponseDto> {
    return this.http.post<AuthResponseDto>(`${this.apiUrl}/auth/organization`, { organizationId } as SwitchOrganizationRequestDto);
  }

  // Password reset endpoints
  requestPasswordReset(data: PasswordResetRequestDto): Observable<PasswordResetResponseDto> {
    return this.http.post<PasswordResetResponseDto>(`${this.apiUrl}/auth/password/send`, data);
  }

  resetPassword(data: PasswordResetChangeDto): Observable<PasswordResetResponseDto> {
    return this.http.post<PasswordResetResponseDto>(`${this.apiUrl}/auth/password/reset`, data);
  }
}
