import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { CoreUrls } from '../../core-urls';

@Component({
  selector: 'app-request-password-reset',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink],
  templateUrl: './request-password-reset.component.html',
  styleUrls: ['./request-password-reset.component.scss']
})
export class RequestPasswordResetComponent implements OnInit {
  requestForm: FormGroup;
  error: string | null = null;
  loading = false;
  success = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.requestForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  ngOnInit(): void {
    // Check if user is already authenticated
    if (this.authService.isAuthenticated()) {
      console.log('User already authenticated, redirecting to dashboard');
      this.router.navigateByUrl(CoreUrls.dashboard);
    }
  }

  onSubmit() {
    if (this.requestForm.invalid) {
      return;
    }

    this.loading = true;
    this.error = null;
    this.success = false;

    this.authService.requestPasswordReset(this.requestForm.value.email).subscribe({
      next: (response) => {
        this.loading = false;
        this.success = true;
        console.log('Password reset email sent:', response.message);
      },
      error: (err) => {
        this.loading = false;
        this.error = err.error?.message || 'Failed to send password reset email. Please try again.';
      }
    });
  }

  protected readonly CoreUrls = CoreUrls;
}
