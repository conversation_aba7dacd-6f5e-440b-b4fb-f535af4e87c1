import { Routes } from '@angular/router';
import { authGuard } from './core/guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent)
  },
  {
    path: 'auth/login',
    loadComponent: () => import('./core/pages/login/login.component').then(m => m.LoginComponent),
  },
  {
    path: 'auth/register',
    loadComponent: () => import('./core/pages/register/register.component').then(m => m.RegisterComponent),
  },
  {
    path: 'auth/verify-email',
    loadComponent: () => import('./core/pages/verify-email/verify-email.component').then(m => m.VerifyEmailComponent),
  },
  {
    path: 'auth/request-password-reset',
    loadComponent: () => import('./core/pages/request-password-reset/request-password-reset.component').then(m => m.RequestPasswordResetComponent),
  },
  {
    path: 'auth/reset-password',
    loadComponent: () => import('./core/pages/reset-password/reset-password.component').then(m => m.ResetPasswordComponent),
  },
  {
    path: 'app',
    canActivate: [authGuard],
    loadComponent: () => import('./core/layout/main-layout/main-layout.component').then(m => m.MainLayoutComponent),
    children: [
      {
        path: '',
        redirectTo: '/app/dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./core/pages/dashboard/dashboard.component').then(m => m.DashboardComponent)
      },
      {
        path: 'organizations/create',
        loadComponent: () => import('./core/pages/create-organization/create-organization.component').then(m => m.CreateOrganizationComponent)
      },
      {
        path: 'qrcodes',
        loadComponent: () => import('./pages/qrcodes/qrcodes-list/qrcodes-list.component').then(m => m.QrcodesListComponent)
      },
      {
        path: 'qrcodes/create',
        loadComponent: () => import('./pages/qrcodes/create-qrcode-step-1/create-qrcode-step-1.component').then(m => m.CreateQrcodeStep1Component)
      },
      {
        path: 'qrcodes/create/text',
        loadComponent: () => import('./pages/qrcodes/create-static-text-qrcode/create-static-text-qrcode.component').then(m => m.CreateStaticTextQrcodeComponent)
      },
      {
        path: 'qrcodes/create/link',
        loadComponent: () => import('./pages/qrcodes/create-static-link-qrcode/create-static-link-qrcode.component').then(m => m.CreateStaticLinkQrcodeComponent)
      },
      {
        path: 'qrcodes/create/wifi',
        loadComponent: () => import('./pages/qrcodes/create-static-wifi-qrcode/create-static-wifi-qrcode.component').then(m => m.CreateStaticWifiQrcodeComponent)
      },
      {
        path: 'qrcodes/create/vcard',
        loadComponent: () => import('./pages/qrcodes/create-static-vcard-qrcode/create-static-vcard-qrcode.component').then(m => m.CreateStaticVcardQrcodeComponent)
      },
      {
        path: 'qrcodes/create/me-card',
        loadComponent: () => import('./pages/qrcodes/create-static-mecard-qrcode/create-static-mecard-qrcode.component').then(m => m.CreateStaticMecardQrcodeComponent)
      }
    ]
  },
  {
    path: ':id',
    loadComponent: () => import('./pages/dynamic-page/dynamic-page.component').then(m => m.DynamicPageComponent),
  },
  {
    path: '**',
    redirectTo: ''
  }
];
